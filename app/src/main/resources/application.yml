server:
  port: 8101

spring:
  application:
    name: user
  datasource:
    url: ${SPRING_DATASOURCE_URL:***************************************}
    username: ${SPRING_DATASOURCE_USERNAME:root}
    password: ${SPRING_DATASOURCE_PASSWORD:root}
  jpa:
    hibernate:
      ddl-auto: none
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL8Dialect

management:
  endpoints:
    web:
      exposure:
        include: health,info
